# AUBO 咖啡制作系统

基于双臂机器人的智能咖啡制作系统，支持美式咖啡、拿铁和卡布奇诺的自动制作。

## 快速开始

```cpp
#include "coffee_system.h"

int main() {
    aubo::CoffeeSystem coffee_system;

    // 初始化系统
    if (!coffee_system.initialize()) {
        return 1;
    }

    // 制作拿铁咖啡
    aubo::CoffeeOrder order("ORDER_001", aubo::CoffeeType::LATTE,
                           aubo::LatteArtType::HEART, 1);
    coffee_system.make_coffee(order);

    coffee_system.shutdown();
    return 0;
}
```

## 支持的咖啡类型

- **美式咖啡** - 无拉花，快速制作
- **拿铁咖啡** - 支持心形、叶子、郁金香、天鹅拉花
- **卡布奇诺** - 仅支持心形拉花



## 编译和运行

```bash
mkdir build && cd build
cmake ..
make

# 运行演示程序
./examples/coffee_demo
```

## 系统架构

```mermaid
graph TD
    A[用户应用] --> B[CoffeeSystem]
    B --> C[CoffeeWorkflowEngine]
    C --> D[LeftRobot]
    C --> E[RightRobot]
```

系统采用分层架构，用户只需与 `CoffeeSystem` 交互，底层自动处理工作流选择和机器人控制。

## 工作流选择

```mermaid
flowchart TD
    A[CoffeeOrder] --> B{咖啡类型}

    B -->|AMERICANO| C[americano.json<br/>~90秒]
    B -->|LATTE| D{拉花类型}
    B -->|CAPPUCCINO| E[cappuccino_heart.json<br/>~200秒]

    D -->|HEART| F[latte_heart.json<br/>~180秒]
    D -->|LEAF| G[latte_leaf.json<br/>~200秒]
    D -->|TULIP| H[latte_tulip.json<br/>~220秒]
    D -->|SWAN| I[latte_swan.json<br/>~250秒]

    C --> J[双臂协调制作]
    E --> J
    F --> J
    G --> J
    H --> J
    I --> J
```