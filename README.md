# AUBO 咖啡制作系统

基于双臂机器人的智能咖啡制作系统，支持美式咖啡、拿铁和卡布奇诺的自动制作。

## 快速开始

```cpp
#include "coffee_system.h"

int main() {
    aubo::CoffeeSystem coffee_system;

    // 初始化系统
    if (!coffee_system.initialize()) {
        return 1;
    }

    // 制作拿铁咖啡
    aubo::CoffeeOrder order("ORDER_001", aubo::CoffeeType::LATTE,
                           aubo::LatteArtType::HEART, 1);
    coffee_system.make_coffee(order);

    coffee_system.shutdown();
    return 0;
}
```

## 支持的咖啡类型

- **美式咖啡** - 无拉花，快速制作
- **拿铁咖啡** - 支持心形、叶子、郁金香、天鹅拉花
- **卡布奇诺** - 仅支持心形拉花

## 核心API

```mermaid
classDiagram
    class CoffeeSystem {
        -Impl* impl_
        +initialize() bool
        +shutdown() bool
        +make_coffee(order) bool
        +get_supported_coffee_types() vector~CoffeeType~
        +is_making_coffee() bool
        +get_current_step() string
        +get_status() CoffeeStatus
        +stop_current_process() bool
        +emergency_stop() bool
    }

    class CoffeeOrder {
        +string order_id
        +CoffeeType type
        +LatteArtType latte_art
        +int quantity
        +needs_milk() bool
        +needs_latte_art() bool
        +get_workflow_name() string
        +is_valid_combination() bool
    }

    class CoffeeType {
        <<enumeration>>
        AMERICANO
        LATTE
        CAPPUCCINO
    }

    class LatteArtType {
        <<enumeration>>
        NONE
        HEART
        LEAF
        TULIP
        SWAN
    }

    class CoffeeStatus {
        <<enumeration>>
        IDLE
        INITIALIZING
        PREPARING
        MAKING
        FINISHING
        COMPLETED
        ERROR
    }

    CoffeeSystem --> CoffeeOrder : uses
    CoffeeOrder --> CoffeeType : contains
    CoffeeOrder --> LatteArtType : contains
    CoffeeSystem --> CoffeeStatus : returns
```

## 编译和运行

```bash
mkdir build && cd build
cmake ..
make

# 运行演示程序
./examples/coffee_demo
```

## 系统架构

```mermaid
graph TD
    A[用户应用] --> B[CoffeeSystem]
    B --> C[CoffeeWorkflowEngine]
    C --> D[LeftRobot]
    C --> E[RightRobot]

    B --> F[CoffeeOrder验证]
    B --> G[工作流选择]

    F --> H{订单有效?}
    H -->|是| G
    H -->|否| I[返回错误]

    G --> J[加载工作流配置]
    J --> C

    C --> K[并行执行控制]
    K --> D
    K --> E

    D --> L[左臂动作<br/>取杯子<br/>取咖啡<br/>拉花]
    E --> M[右臂动作<br/>取奶<br/>摇奶<br/>倒奶]
```

系统自动根据咖啡类型选择合适的制作工作流，用户无需关心底层实现细节。

## 工作流选择

```mermaid
graph TD
    A[CoffeeOrder] --> B{咖啡类型}

    B -->|AMERICANO| C[americano.json]
    B -->|LATTE| D{拉花类型}
    B -->|CAPPUCCINO| E[cappuccino_heart.json]

    D -->|HEART| F[latte_heart.json]
    D -->|LEAF| G[latte_leaf.json]
    D -->|TULIP| H[latte_tulip.json]
    D -->|SWAN| I[latte_swan.json]

    C --> J[工作流执行]
    E --> J
    F --> J
    G --> J
    H --> J
    I --> J

    J --> K[双臂协调制作]
```