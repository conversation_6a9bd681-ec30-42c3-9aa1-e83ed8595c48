# AUBO 咖啡制作系统

基于双臂机器人的智能咖啡制作系统，支持美式咖啡、拿铁和卡布奇诺的自动制作。

## 快速开始

```cpp
#include "coffee_system.h"

int main() {
    aubo::CoffeeSystem coffee_system;

    // 初始化系统
    if (!coffee_system.initialize()) {
        return 1;
    }

    // 制作拿铁咖啡
    aubo::CoffeeOrder order("ORDER_001", aubo::CoffeeType::LATTE,
                           aubo::LatteArtType::HEART, 1);
    coffee_system.make_coffee(order);

    coffee_system.shutdown();
    return 0;
}
```

## 支持的咖啡类型

- **美式咖啡** - 无拉花，快速制作
- **拿铁咖啡** - 支持心形、叶子、郁金香、天鹅拉花
- **卡布奇诺** - 默认心形拉花



## 编译和运行

```bash
mkdir build && cd build
cmake ..
make

# 运行演示程序
./bin/coffee_demo
```

## 系统架构

```mermaid
graph TD
    A[咖啡制作系统] --> B[工作流引擎]
    B --> C[左臂机器人]
    B --> D[右臂机器人]
```

系统采用分层架构，咖啡制作系统作为统一接口，底层自动处理工作流选择和机器人控制。

## 工作流选择

```mermaid
flowchart TD
    A[咖啡订单] --> B{咖啡类型}

    B -->|美式| C[制作美式咖啡]
    B -->|拿铁| D{拉花类型}
    B -->|卡布奇诺| E[制作心形卡布奇诺]

    D -->|心形| F[制作心形拿铁]
    D -->|叶子| G[制作叶子拿铁]
    D -->|郁金香| H[制作郁金香拿铁]
    D -->|天鹅| I[制作天鹅拿铁]

    C --> J[执行制作流程]
    E --> J
    F --> J
    G --> J
    H --> J
    I --> J

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style D fill:#fff3e0
    style J fill:#f1f8e9
```