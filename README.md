# AUBO 咖啡制作系统

基于双臂机器人的智能咖啡制作系统，支持美式咖啡、拿铁和卡布奇诺的自动制作。

## 快速开始

```cpp
#include "coffee_system.h"

int main() {
    aubo::CoffeeSystem coffee_system;

    // 初始化系统
    if (!coffee_system.initialize()) {
        return 1;
    }

    // 制作拿铁咖啡
    aubo::CoffeeOrder order("ORDER_001", aubo::CoffeeType::LATTE,
                           aubo::LatteArtType::HEART, 1);
    coffee_system.make_coffee(order);

    coffee_system.shutdown();
    return 0;
}
```

## 支持的咖啡类型

- **美式咖啡** - 无拉花，快速制作
- **拿铁咖啡** - 支持心形、叶子、郁金香、天鹅拉花
- **卡布奇诺** - 仅支持心形拉花

## 核心API

### CoffeeSystem

```cpp
class CoffeeSystem {
public:
    // 系统管理
    bool initialize();                    // 初始化系统
    bool shutdown();                      // 关闭系统

    // 咖啡制作
    bool make_coffee(const CoffeeOrder& order);

    // 状态查询
    std::vector<CoffeeType> get_supported_coffee_types() const;
    bool is_making_coffee() const;
    std::string get_current_step() const;
    CoffeeStatus get_status() const;

    // 控制功能
    bool stop_current_process();
    bool emergency_stop();
};
```

### CoffeeOrder

```cpp
struct CoffeeOrder {
    std::string order_id;       // 订单ID
    CoffeeType type;            // 咖啡类型
    LatteArtType latte_art;     // 拉花类型
    int quantity;               // 数量

    CoffeeOrder(const std::string& order_id = "",
                CoffeeType coffee_type = CoffeeType::AMERICANO,
                LatteArtType latte_art_type = LatteArtType::NONE,
                int quantity = 1);
};
```

## 编译和运行

```bash
mkdir build && cd build
cmake ..
make

# 运行演示程序
./examples/coffee_demo
```

## 系统架构

```
用户应用
    ↓
CoffeeSystem (统一接口)
    ↓
CoffeeWorkflowEngine (工作流执行)
    ↓
LeftRobot + RightRobot (机器人控制)
```

系统自动根据咖啡类型选择合适的制作工作流，用户无需关心底层实现细节。