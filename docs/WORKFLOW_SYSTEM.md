# 咖啡制作工作流系统

## 概述

咖啡制作工作流系统是一个基于JSON配置的灵活咖啡制作流程管理系统。它允许您通过配置文件定义完整的咖啡制作过程，包括每个机器人臂要执行的动作、等待时间、并行执行策略等。

## 🆕 新特性：模块化配置

现在支持为每种咖啡创建单独的配置文件，让管理更加简单和模块化：

### 配置文件结构
```
share/config/workflows/
├── americano.json           # 美式咖啡配置
├── latte.json              # 拿铁咖啡配置
├── cappuccino.json         # 卡布奇诺配置
└── mocha.json              # 摩卡咖啡配置
```

### 加载方式

**从目录加载** (推荐)
```cpp
coffee.load_workflows_from_directory("share/config/workflows");
```

## 核心特性

### 🔧 灵活的工作流配置
- **JSON配置**: 使用JSON格式定义工作流，易于编辑和维护
- **步骤化执行**: 将复杂的咖啡制作过程分解为清晰的步骤
- **参数化动作**: 支持为每个动作设置自定义参数

### 🤖 双臂协调控制
- **并行执行**: 左右臂可以同时执行不同任务，提高效率
- **顺序执行**: 需要协调的动作可以按顺序执行
- **智能调度**: 自动处理任务依赖关系

### ⏱️ 时间管理
- **超时控制**: 每个动作都有超时保护
- **等待步骤**: 支持在工作流中插入等待时间
- **重试机制**: 失败时可以自动重试

### 🛡️ 安全保障
- **紧急停止**: 支持随时停止当前工作流
- **错误处理**: 完善的错误检测和处理机制
- **状态监控**: 实时监控工作流执行状态

## 工作流配置格式

### 基本结构

```json
{
  "workflows": {
    "workflow_name": {
      "name": "工作流显示名称",
      "description": "工作流描述",
      "steps": [
        {
          "id": "step_1",
          "name": "步骤名称",
          "type": "action|wait|condition",
          "execution": "sequential|parallel",
          "actions": [...],
          "duration": 5
        }
      ]
    }
  },
  "global_settings": {
    "default_timeout": 30,
    "retry_attempts": 2,
    "emergency_stop_on_failure": true,
    "parallel_execution_delay": 0.5
  }
}
```

### 步骤类型

#### 1. 动作步骤 (action)
执行具体的机器人动作：

```json
{
  "id": "step_1",
  "name": "取杯子",
  "type": "action",
  "execution": "sequential",
  "actions": [
    {
      "robot": "left",
      "action": "get_cup",
      "timeout": 30
    }
  ]
}
```

#### 2. 等待步骤 (wait)
在工作流中插入等待时间：

```json
{
  "id": "step_2",
  "name": "等待奶泡稳定",
  "type": "wait",
  "duration": 5
}
```

#### 3. 条件步骤 (condition)
根据条件执行不同分支（暂未实现）。

### 执行类型

- **sequential**: 顺序执行，一个动作完成后再执行下一个
- **parallel**: 并行执行，多个动作同时开始

### 支持的机器人动作

#### 左臂动作 (left)
- `get_cup`: 取杯子
- `get_coffee`: 取咖啡
- `prepare_for_latte_art`: 准备拉花
- `do_latte_art`: 执行拉花
- `deliver_coffee`: 交付咖啡
- `move_to_home`: 移动到初始位置

#### 右臂动作 (right)
- `get_milk`: 取奶
- `shake_milk`: 摇奶
- `prepare_for_latte_art`: 准备拉花
- `do_latte_art`: 执行拉花
- `pour_remaining_milk`: 倒剩余牛奶
- `clean`: 清洗
- `move_to_home`: 移动到初始位置

### 动作参数

某些动作支持自定义参数：

```json
{
  "robot": "right",
  "action": "shake_milk",
  "parameters": {
    "intensity": "high",
    "duration": 25,
    "pattern": "circular"
  },
  "timeout": 30
}
```

## 预定义工作流

系统提供了几个预定义的工作流：

### 1. americano (美式咖啡)
- 简单的美式咖啡制作
- 无需牛奶和拉花
- 适合快速制作

### 2. latte (拿铁咖啡)
- 包含牛奶和拉花的拿铁制作
- 左右臂并行工作
- 支持心形拉花

### 3. cappuccino (卡布奇诺)
- 特殊的奶泡处理
- 高强度摇奶
- 支持叶子拉花

### 4. mocha (摩卡咖啡)
- 包含巧克力糖浆
- 中等强度摇奶
- 支持郁金香拉花

### 5. custom_workflow (自定义工作流)
- 展示高级功能
- 复杂的参数配置
- 精细的时间控制

## 使用方法

### 1. 初始化系统

```cpp
#include "coffee.h"

aubo::Coffee coffee;

// 初始化咖啡制作系统
if (!coffee.init()) {
    std::cerr << "系统初始化失败" << std::endl;
    return -1;
}
```

### 2. 加载工作流配置

```cpp
// 加载工作流配置文件
if (!coffee.load_workflows("share/config/coffee_workflows.json")) {
    std::cerr << "工作流配置加载失败" << std::endl;
    return -1;
}
```

### 3. 查看可用工作流

```cpp
// 获取可用工作流列表
auto workflows = coffee.get_available_workflows();
for (const auto& workflow : workflows) {
    std::cout << "可用工作流: " << workflow << std::endl;
}
```

### 4. 执行工作流

```cpp
// 创建咖啡订单
aubo::CoffeeOrder order;
order.order_id = "WF001";
order.type = aubo::CoffeeType::LATTE;
order.latte_art = aubo::LatteArtType::HEART;
order.quantity = 1;

// 使用工作流制作咖啡
if (coffee.make_coffee_with_workflow("latte", order)) {
    std::cout << "咖啡制作完成!" << std::endl;
} else {
    std::cout << "咖啡制作失败!" << std::endl;
}
```

### 5. 监控执行状态

```cpp
// 检查是否正在执行工作流
if (coffee.is_workflow_executing()) {
    std::cout << "当前步骤: " << coffee.get_current_workflow_step() << std::endl;
}

// 获取系统状态
std::cout << "系统状态: " << coffee.get_status_string() << std::endl;
```

### 6. 停止工作流

```cpp
// 停止当前执行的工作流
if (coffee.stop_current_workflow()) {
    std::cout << "工作流已停止" << std::endl;
}
```

## 示例程序

运行工作流演示程序：

```bash
# 编译项目
mkdir build && cd build
cmake ..
make

# 运行工作流演示
./examples/workflow_demo
```

演示程序提供了交互式菜单，您可以：
- 初始化系统
- 加载工作流配置
- 查看可用工作流
- 制作不同类型的咖啡
- 监控系统状态
- 停止工作流执行

## 自定义工作流

您可以创建自己的工作流配置：

1. **编辑配置文件**: 修改 `share/config/coffee_workflows.json`
2. **定义新工作流**: 添加新的工作流定义
3. **测试工作流**: 使用演示程序测试新工作流
4. **优化参数**: 根据实际情况调整超时时间和参数

## 最佳实践

### 1. 工作流设计
- 将复杂流程分解为简单步骤
- 合理使用并行执行提高效率
- 为每个步骤设置合适的超时时间

### 2. 错误处理
- 启用紧急停止功能
- 设置合理的重试次数
- 监控工作流执行状态

### 3. 性能优化
- 利用双臂并行工作
- 减少不必要的等待时间
- 优化动作序列

### 4. 安全考虑
- 设置合理的超时时间
- 实现紧急停止机制
- 定期检查机器人状态

## 故障排除

### 常见问题

1. **工作流加载失败**
   - 检查JSON格式是否正确
   - 确认文件路径是否存在
   - 查看日志获取详细错误信息

2. **动作执行失败**
   - 检查机器人是否正确初始化
   - 确认动作名称是否正确
   - 调整超时时间设置

3. **并行执行问题**
   - 确保动作之间没有冲突
   - 检查资源占用情况
   - 调整并行执行延迟

### 调试技巧

- 启用详细日志记录
- 使用单步执行模式
- 监控系统状态变化
- 检查配置文件语法

## 扩展开发

工作流系统支持扩展：

1. **添加新动作**: 在机器人类中实现新动作
2. **自定义参数**: 扩展动作参数支持
3. **条件执行**: 实现条件步骤功能
4. **外部集成**: 与其他系统集成

通过工作流系统，您可以轻松配置和管理复杂的咖啡制作流程，实现高效、灵活、安全的自动化咖啡制作。
