# 工作流配置系统迁移指南

## 概述

咖啡制作系统已经完全迁移到新的模块化工作流配置系统。原来的单文件配置 (`coffee_workflows.json`) 已被移除，现在使用更灵活的分离式配置文件。

## 🔄 主要变化

### 配置文件结构变化

**之前 (已移除)**:
```
share/config/
└── coffee_workflows.json    # 所有工作流在一个文件中
```

**现在**:
```
share/config/workflows/
├── workflow_registry.json   # 工作流注册表
├── americano.json          # 美式咖啡配置
├── latte.json             # 拿铁咖啡配置
├── cappuccino.json        # 卡布奇诺配置
├── mocha.json             # 摩卡咖啡配置
└── README.md              # 详细说明
```

### API 变化

**移除的方法**:
```cpp
// 这些方法已被移除
coffee.load_workflows("share/config/coffee_workflows.json");
workflow_engine.load_workflows_from_file(config_path);
```

**新的方法**:
```cpp
// 推荐使用
coffee.load_workflows_from_registry("share/config/workflows/workflow_registry.json");

// 或者
coffee.load_workflows_from_directory("share/config/workflows");
```

## 🚀 迁移步骤

### 1. 更新代码

如果您的代码中使用了旧的加载方法，请更新为新方法：

**旧代码**:
```cpp
// 需要更新
if (!coffee.load_workflows("share/config/coffee_workflows.json")) {
    std::cerr << "工作流加载失败" << std::endl;
    return -1;
}
```

**新代码**:
```cpp
// 推荐方式
if (!coffee.load_workflows_from_registry("share/config/workflows/workflow_registry.json")) {
    std::cerr << "工作流注册表加载失败" << std::endl;
    return -1;
}

// 或者简单方式
if (!coffee.load_workflows_from_directory("share/config/workflows")) {
    std::cerr << "工作流目录加载失败" << std::endl;
    return -1;
}
```

### 2. 更新配置路径

确保您的应用程序指向正确的配置路径：

```cpp
// 使用新的配置路径
std::string registry_path = "share/config/workflows/workflow_registry.json";
std::string workflow_dir = "share/config/workflows";
```

### 3. 验证功能

运行演示程序验证新配置系统：

```bash
./examples/workflow_demo
```

选择以下选项进行测试：
- 选项1: 初始化系统
- 选项2: 从注册表加载工作流 (推荐)
- 选项4: 查看可用工作流
- 选项7-10: 测试各种咖啡制作

## 🎯 新功能优势

### 1. 模块化管理
- 每种咖啡独立配置文件
- 更容易维护和调试
- 支持并行开发

### 2. 丰富的元信息
- 工作流分类和难度等级
- 详细的参数说明
- 完整的标签系统

### 3. 灵活的加载方式
- 注册表加载 (包含完整元信息)
- 目录扫描加载 (自动发现)
- 单文件加载 (按需加载)

### 4. 增强的查询功能
```cpp
// 新增功能
auto categories = coffee.get_workflow_categories();
auto workflows = coffee.get_workflows_by_category("milk_based");
std::string info = coffee.get_workflow_info("latte");
```

## 📝 配置文件格式

### 单个工作流文件格式
```json
{
  "workflow": {
    "name": "拿铁咖啡制作流程",
    "description": "包含牛奶和拉花的拿铁咖啡制作",
    "coffee_type": "latte",
    "estimated_time": 180,
    "difficulty": "medium",
    "steps": [...]
  },
  "settings": {
    "quality_check": true,
    "temperature_monitoring": true
  },
  "metadata": {
    "version": "1.0",
    "tags": ["milk", "latte-art"]
  }
}
```

### 注册表文件格式
```json
{
  "workflow_registry": {
    "workflows": [
      {
        "id": "latte",
        "name": "拿铁咖啡",
        "file": "latte.json",
        "category": "milk_based",
        "difficulty": "medium"
      }
    ],
    "categories": {...},
    "difficulty_levels": {...}
  }
}
```

## 🔧 自定义配置

### 添加新工作流

1. **创建工作流文件**:
   ```bash
   cp share/config/workflows/latte.json share/config/workflows/my_coffee.json
   ```

2. **编辑配置内容**:
   - 修改 `coffee_type` 为新的ID
   - 更新名称和描述
   - 调整制作步骤

3. **更新注册表** (如果使用注册表加载):
   在 `workflow_registry.json` 中添加新工作流信息

### 修改现有工作流

直接编辑对应的JSON文件，系统会自动重新加载。

## 🛠️ 故障排除

### 常见问题

1. **找不到配置文件**
   - 确认文件路径正确
   - 检查文件是否存在
   - 验证文件权限

2. **JSON格式错误**
   - 使用JSON验证工具检查语法
   - 查看详细错误日志
   - 参考示例文件格式

3. **工作流加载失败**
   - 检查注册表中的文件名是否正确
   - 确认工作流ID匹配
   - 验证必需字段是否完整

### 调试技巧

- 启用详细日志记录
- 使用演示程序测试
- 逐个验证配置文件
- 检查系统状态

## 📚 相关文档

- [工作流系统详细文档](WORKFLOW_SYSTEM.md)
- [配置文件说明](../share/config/workflows/README.md)
- [演示程序使用指南](../examples/README.md)

## 🎉 总结

新的模块化工作流配置系统提供了：
- ✅ 更好的可维护性
- ✅ 更灵活的配置管理
- ✅ 更丰富的功能特性
- ✅ 更简单的扩展方式

迁移过程简单直接，新系统完全向后兼容核心功能，同时提供了更多高级特性。
