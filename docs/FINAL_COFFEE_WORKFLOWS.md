# 咖啡工作流配置

## 支持的咖啡类型

- **美式咖啡** - 无拉花，工作流: `americano.json`
- **拿铁咖啡** - 支持4种拉花：心形、叶子、郁金香、天鹅
- **卡布奇诺** - 仅支持心形拉花

## 智能工作流选择

系统根据咖啡类型和拉花类型自动选择工作流：

<augment_code_snippet path="src/coffee_types.h" mode="EXCERPT">
````cpp
std::string get_workflow_name() const {
    switch (type) {
        case CoffeeType::AMERICANO: return "americano";
        case CoffeeType::LATTE:
            switch (latte_art) {
                case LatteArtType::HEART: return "latte_heart";
                case LatteArtType::LEAF: return "latte_leaf";
                case LatteArtType::TULIP: return "latte_tulip";
                case LatteArtType::SWAN: return "latte_swan";
                default: return "latte_heart";
            }
        case CoffeeType::CAPPUCCINO: return "cappuccino_heart";
        default: return "americano";
    }
}
````
</augment_code_snippet>

## 订单验证

<augment_code_snippet path="src/coffee_types.h" mode="EXCERPT">
````cpp
bool is_valid_combination() const {
    switch (type) {
        case CoffeeType::AMERICANO:
            return latte_art == LatteArtType::NONE;
        case CoffeeType::LATTE:
            return latte_art == LatteArtType::HEART ||
                   latte_art == LatteArtType::LEAF ||
                   latte_art == LatteArtType::TULIP ||
                   latte_art == LatteArtType::SWAN;
        case CoffeeType::CAPPUCCINO:
            return latte_art == LatteArtType::HEART;
        default: return false;
    }
}
````
</augment_code_snippet>

## 配置文件结构

```
share/config/workflows/
├── americano.json              # 美式咖啡
├── latte_heart.json           # 拿铁 + 心形拉花
├── latte_leaf.json            # 拿铁 + 叶子拉花
├── latte_tulip.json           # 拿铁 + 郁金香拉花
├── latte_swan.json            # 拿铁 + 天鹅拉花
└── cappuccino_heart.json      # 卡布奇诺 + 心形拉花
```

## 制作时间

- **美式咖啡**: ~90秒
- **拿铁心形**: ~180秒
- **拿铁叶子**: ~200秒
- **拿铁郁金香**: ~220秒
- **拿铁天鹅**: ~250秒
- **卡布奇诺**: ~200秒

## 拿铁制作流程

```mermaid
sequenceDiagram
    participant U as 用户应用
    participant CS as CoffeeSystem
    participant WE as WorkflowEngine
    participant LR as LeftRobot
    participant RR as RightRobot

    U->>CS: make_coffee(latte_order)
    CS->>CS: 验证订单
    CS->>CS: 选择工作流(latte_heart)
    CS->>WE: 执行工作流

    par 并行执行
        WE->>LR: 取杯子
        and
        WE->>RR: 取奶
    end

    WE->>LR: 取咖啡
    WE->>RR: 摇奶(25秒)

    par 拉花阶段
        WE->>LR: 准备拉花
        and
        WE->>RR: 准备拉花
    end

    WE->>LR: 执行心形拉花
    WE->>RR: 倒剩余牛奶
    WE->>LR: 交付咖啡

    WE->>CS: 制作完成
    CS->>U: 返回成功
```
