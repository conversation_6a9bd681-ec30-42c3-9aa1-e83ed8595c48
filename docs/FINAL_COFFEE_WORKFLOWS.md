# 咖啡工作流配置

## 支持的咖啡类型

- **美式咖啡** - 无拉花
- **拿铁咖啡** - 支持心形、叶子、郁金香、天鹅拉花
- **卡布奇诺** - 默认心形拉花

## 工作流文件

系统根据咖啡类型和拉花类型自动选择对应的工作流配置文件：

```
share/config/workflows/
├── americano.json              # 美式咖啡
├── latte_heart.json           # 拿铁 + 心形拉花
├── latte_leaf.json            # 拿铁 + 叶子拉花
├── latte_tulip.json           # 拿铁 + 郁金香拉花
├── latte_swan.json            # 拿铁 + 天鹅拉花
└── cappuccino_heart.json      # 卡布奇诺 + 心形拉花
```

## 制作流程示例：心形拿铁

```mermaid
sequenceDiagram
    participant 用户
    participant 咖啡制作系统
    participant 左臂机器人
    participant 右臂机器人

    用户->>咖啡制作系统: 制作心形拿铁
    咖啡制作系统->>咖啡制作系统: 选择latte_heart工作流

    par 并行准备
        咖啡制作系统->>左臂机器人: 取杯子
        and
        咖啡制作系统->>右臂机器人: 取牛奶
    end

    咖啡制作系统->>左臂机器人: 制作咖啡基底
    咖啡制作系统->>右臂机器人: 制作奶泡

    par 拉花制作
        咖啡制作系统->>左臂机器人: 准备拉花
        and
        咖啡制作系统->>右臂机器人: 倒奶做心形
    end

    咖啡制作系统->>左臂机器人: 交付咖啡
    咖啡制作系统->>用户: 心形拿铁制作完成
```

系统通过双臂协调制作，实现高效的并行操作和精美的拉花效果。
